<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { 
  MenuFoldOutlined, 
  MenuUnfoldOutlined,
  DashboardOutlined,
  UserOutlined,
  ShoppingOutlined,
  SettingOutlined,
  LogoutOutlined
} from '@ant-design/icons-vue';
import { useAuthStore } from '../stores/auth';

const collapsed = ref(false);
const selectedKeys = ref<string[]>(['dashboard']);
const router = useRouter();
const authStore = useAuthStore();

const username = computed(() => authStore.user?.username || 'User');

// Update selected menu based on current route
const updateSelectedMenu = () => {
  const path = router.currentRoute.value.path;
  if (path.includes('/dashboard')) {
    selectedKeys.value = ['dashboard'];
  } else if (path.includes('/users')) {
    selectedKeys.value = ['users'];
  } else if (path.includes('/products')) {
    selectedKeys.value = ['products'];
  } else if (path.includes('/settings')) {
    selectedKeys.value = ['settings'];
  }
};

// Call on route change
router.afterEach(() => {
  updateSelectedMenu();
});

// Initialize
updateSelectedMenu();

const handleLogout = () => {
  authStore.logout();
  router.push('/auth/login');
};
</script>

<template>
  <a-layout class="dashboard-layout">
    <a-layout-sider
      v-model:collapsed="collapsed"
      theme="dark"
      collapsible
    >
      <div class="logo">
        <span v-if="!collapsed">Admin Panel</span>
        <span v-else>AP</span>
      </div>
      <a-menu
        v-model:selectedKeys="selectedKeys"
        theme="dark"
        mode="inline"
      >
        <a-menu-item key="dashboard" @click="() => router.push('/dashboard')">
          <template #icon><dashboard-outlined /></template>
          <span>Dashboard</span>
        </a-menu-item>
        <a-menu-item key="users" @click="() => router.push('/users')">
          <template #icon><user-outlined /></template>
          <span>Users</span>
        </a-menu-item>
        <a-menu-item key="products" @click="() => router.push('/products')">
          <template #icon><shopping-outlined /></template>
          <span>Products</span>
        </a-menu-item>
        <a-menu-item key="settings" @click="() => router.push('/settings')">
          <template #icon><setting-outlined /></template>
          <span>Settings</span>
        </a-menu-item>
      </a-menu>
    </a-layout-sider>
    <a-layout>
      <a-layout-header class="dashboard-header">
        <div class="header-left">
          <component :is="collapsed ? MenuUnfoldOutlined : MenuFoldOutlined" 
            class="trigger" 
            @click="() => (collapsed = !collapsed)" 
          />
        </div>
        <div class="header-right">
          <a-dropdown>
            <a class="user-dropdown">
              <a-avatar>{{ username.charAt(0).toUpperCase() }}</a-avatar>
              <span class="username">{{ username }}</span>
            </a>
            <template #overlay>
              <a-menu>
                <a-menu-item key="settings" @click="() => router.push('/settings')">
                  <template #icon><setting-outlined /></template>
                  Settings
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item key="logout" @click="handleLogout">
                  <template #icon><logout-outlined /></template>
                  Logout
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </a-layout-header>
      <a-layout-content class="dashboard-content">
        <router-view />
      </a-layout-content>
      <a-layout-footer class="dashboard-footer">
        Admin Dashboard ©{{ new Date().getFullYear() }} Created by Your Company
      </a-layout-footer>
    </a-layout>
  </a-layout>
</template>

<style scoped>
.dashboard-layout {
  min-height: 100vh;
}

.logo {
  height: 32px;
  margin: 16px;
  color: white;
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
}

.dashboard-header {
  background: #fff;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  z-index: 1;
}

.header-left,
.header-right {
  padding: 0 24px;
  display: flex;
  align-items: center;
}

.trigger {
  font-size: 18px;
  cursor: pointer;
  transition: color 0.3s;
}

.trigger:hover {
  color: #1890ff;
}

.user-dropdown {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.username {
  margin-left: 8px;
  color: rgba(0, 0, 0, 0.65);
  display: inline-block;
}

.dashboard-content {
  margin: 24px 16px;
  padding: 24px;
  background: #fff;
  min-height: 280px;
  overflow: initial;
}

.dashboard-footer {
  text-align: center;
  color: rgba(0, 0, 0, 0.45);
}

@media (max-width: 768px) {
  .username {
    display: none;
  }
  
  .dashboard-content {
    margin: 16px 8px;
    padding: 16px;
  }
}
</style>