<script setup lang="ts">
import { computed } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();
const pageTitle = computed(() => {
  switch (route.name) {
    case 'login':
      return 'Sign in to your account';
    default:
      return 'Admin Dashboard';
  }
});
</script>

<template>
  <div class="auth-layout">
    <div class="auth-container">
      <div class="auth-logo">
        <h1 class="logo-text">Admin Dashboard</h1>
      </div>
      <div class="auth-content">
        <h2 class="auth-title">{{ pageTitle }}</h2>
        <router-view />
      </div>
      <div class="auth-footer">
        <p>© {{ new Date().getFullYear() }} Your Company. All rights reserved.</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.auth-layout {
  display: flex;
  min-height: 100vh;
  align-items: center;
  justify-content: center;
  background-color: #f0f2f5;
}

.auth-container {
  width: 100%;
  max-width: 420px;
  padding: 30px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.auth-logo {
  text-align: center;
  margin-bottom: 24px;
}

.logo-text {
  font-size: 24px;
  font-weight: 600;
  color: #1890ff;
}

.auth-title {
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 24px;
  color: rgba(0, 0, 0, 0.85);
  text-align: center;
}

.auth-footer {
  margin-top: 24px;
  text-align: center;
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
}
</style>