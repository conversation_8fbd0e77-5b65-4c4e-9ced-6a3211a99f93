<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import { ExclamationCircleOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { fetchProducts, deleteProduct } from '../../api/products';
import type { Product } from '../../types/product';

const router = useRouter();
const loading = ref(false);
const products = ref<Product[]>([]);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50'],
});

const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 80,
  },
  {
    title: 'Name',
    dataIndex: 'name',
    key: 'name',
    sorter: true,
  },
  {
    title: 'Category',
    dataIndex: 'category',
    key: 'category',
    filters: [
      { text: 'Electronics', value: 'electronics' },
      { text: 'Clothing', value: 'clothing' },
      { text: 'Books', value: 'books' },
      { text: 'Food', value: 'food' },
    ],
  },
  {
    title: 'Price',
    dataIndex: 'price',
    key: 'price',
    sorter: true,
    customRender: ({ text }: { text: number }) => `$${text.toFixed(2)}`
  },
  {
    title: 'Stock',
    dataIndex: 'stock',
    key: 'stock',
    sorter: true,
  },
  {
    title: 'Status',
    dataIndex: 'status',
    key: 'status',
    filters: [
      { text: 'Active', value: 'active' },
      { text: 'Inactive', value: 'inactive' },
    ],
  },
  {
    title: 'Actions',
    key: 'actions',
    width: 150,
  },
];

const fetchData = async () => {
  loading.value = true;
  try {
    const { data, total } = await fetchProducts({
      page: pagination.current,
      pageSize: pagination.pageSize,
    });
    products.value = data;
    pagination.total = total;
  } catch (error) {
    console.error('Failed to fetch products:', error);
    message.error('Failed to fetch products');
  } finally {
    loading.value = false;
  }
};

const handleTableChange = (pag: any, filters: any, sorter: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchData();
};

const handleEdit = (id: string) => {
  router.push(`/products/${id}/edit`);
};

const handleDelete = (id: string) => {
  Modal.confirm({
    title: 'Are you sure you want to delete this product?',
    icon: () => h(ExclamationCircleOutlined),
    content: 'This action cannot be undone.',
    okText: 'Yes',
    okType: 'danger',
    cancelText: 'No',
    async onOk() {
      try {
        await deleteProduct(id);
        message.success('Product deleted successfully');
        fetchData();
      } catch (error) {
        console.error('Failed to delete product:', error);
        message.error('Failed to delete product');
      }
    },
  });
};

const handleAddProduct = () => {
  router.push('/products/create');
};

onMounted(() => {
  fetchData();
});
</script>

<template>
  <div class="product-list-container">
    <div class="page-header">
      <h1 class="page-title">Products</h1>
      <a-button type="primary" @click="handleAddProduct">
        <template #icon><PlusOutlined /></template>
        Add Product
      </a-button>
    </div>
    
    <a-card>
      <a-table
        :columns="columns"
        :data-source="products"
        :pagination="pagination"
        :loading="loading"
        @change="handleTableChange"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="record.status === 'active' ? 'green' : 'red'">
              {{ record.status }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'actions'">
            <a-space>
              <a-button type="primary" size="small" @click="handleEdit(record.id)">
                Edit
              </a-button>
              <a-button type="danger" size="small" @click="handleDelete(record.id)">
                Delete
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<style scoped>
.product-list-container {
  width: 100%;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}
</style>