<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import { fetchUser, createUser, updateUser } from '../../api/users';
import type { User } from '../../types/auth';

const router = useRouter();
const route = useRoute();
const userId = route.params.id as string;
const isEdit = !!userId;
const loading = ref(false);
const submitting = ref(false);

const formState = reactive<Partial<User>>({
  username: '',
  email: '',
  name: '',
  roles: ['user'],
  status: 'active'
});

const rules = {
  username: [
    { required: true, message: 'Please input username!', trigger: 'blur' },
    { min: 3, message: 'Username must be at least 3 characters', trigger: 'blur' }
  ],
  email: [
    { required: true, message: 'Please input email address!', trigger: 'blur' },
    { type: 'email', message: 'Please enter a valid email address!', trigger: 'blur' }
  ],
  name: [
    { required: true, message: 'Please input full name!', trigger: 'blur' }
  ],
  roles: [
    { required: true, message: 'Please select at least one role!', trigger: 'change' }
  ],
  status: [
    { required: true, message: 'Please select status!', trigger: 'change' }
  ]
};

const roleOptions = [
  { label: 'Admin', value: 'admin' },
  { label: 'Editor', value: 'editor' },
  { label: 'User', value: 'user' }
];

const statusOptions = [
  { label: 'Active', value: 'active' },
  { label: 'Inactive', value: 'inactive' }
];

const fetchUserData = async () => {
  if (!isEdit) return;
  
  loading.value = true;
  try {
    const user = await fetchUser(userId);
    Object.assign(formState, user);
  } catch (error) {
    console.error('Failed to fetch user:', error);
    message.error('Failed to fetch user data');
    router.push('/users');
  } finally {
    loading.value = false;
  }
};

const handleSubmit = async () => {
  submitting.value = true;
  try {
    if (isEdit) {
      await updateUser(userId, formState);
      message.success('User updated successfully');
    } else {
      await createUser(formState);
      message.success('User created successfully');
    }
    router.push('/users');
  } catch (error) {
    console.error('Failed to save user:', error);
    message.error('Failed to save user');
  } finally {
    submitting.value = false;
  }
};

const handleCancel = () => {
  router.push('/users');
};

onMounted(() => {
  fetchUserData();
});
</script>

<template>
  <div class="user-form-container">
    <div class="page-header">
      <h1 class="page-title">{{ isEdit ? 'Edit User' : 'Create User' }}</h1>
    </div>
    
    <a-card :loading="loading">
      <a-form
        :model="formState"
        :rules="rules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 16 }"
        layout="horizontal"
      >
        <a-form-item label="Username" name="username">
          <a-input v-model:value="formState.username" />
        </a-form-item>
        
        <a-form-item label="Email" name="email">
          <a-input v-model:value="formState.email" />
        </a-form-item>
        
        <a-form-item label="Full Name" name="name">
          <a-input v-model:value="formState.name" />
        </a-form-item>
        
        <a-form-item label="Roles" name="roles">
          <a-select
            v-model:value="formState.roles"
            mode="multiple"
            :options="roleOptions"
            placeholder="Select roles"
          />
        </a-form-item>
        
        <a-form-item label="Status" name="status">
          <a-select
            v-model:value="formState.status"
            :options="statusOptions"
            placeholder="Select status"
          />
        </a-form-item>
        
        <a-form-item :wrapper-col="{ offset: 4, span: 16 }">
          <a-space>
            <a-button type="primary" @click="handleSubmit" :loading="submitting">
              {{ isEdit ? 'Update' : 'Create' }}
            </a-button>
            <a-button @click="handleCancel">
              Cancel
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<style scoped>
.user-form-container {
  width: 100%;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}
</style>