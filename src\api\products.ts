import axios from '../utils/axios';
import type { Product, FetchProductsParams, ProductsResponse } from '../types/product';

// Mock data
const mockProducts: Product[] = [
  {
    id: '1',
    name: 'Wireless Headphones',
    description: 'High-quality wireless headphones with noise cancellation',
    category: 'electronics',
    price: 199.99,
    stock: 45,
    status: 'active',
    image: 'https://example.com/headphones.jpg',
    createdAt: '2023-05-10T00:00:00Z',
    updatedAt: '2023-09-15T00:00:00Z'
  },
  {
    id: '2',
    name: 'Cotton T-Shirt',
    description: 'Comfortable cotton t-shirt for everyday wear',
    category: 'clothing',
    price: 24.99,
    stock: 120,
    status: 'active',
    image: 'https://example.com/tshirt.jpg',
    createdAt: '2023-04-20T00:00:00Z',
    updatedAt: '2023-08-05T00:00:00Z'
  },
  {
    id: '3',
    name: 'Smartphone Pro',
    description: 'Latest smartphone with advanced camera features',
    category: 'electronics',
    price: 999.99,
    stock: 18,
    status: 'active',
    image: 'https://example.com/smartphone.jpg',
    createdAt: '2023-06-15T00:00:00Z',
    updatedAt: '2023-09-20T00:00:00Z'
  },
  {
    id: '4',
    name: 'Programming Book',
    description: 'Comprehensive guide to modern programming',
    category: 'books',
    price: 49.99,
    stock: 35,
    status: 'inactive',
    image: 'https://example.com/book.jpg',
    createdAt: '2023-03-12T00:00:00Z',
    updatedAt: '2023-07-30T00:00:00Z'
  },
  // Add more mock products as needed
];

export async function fetchProducts(params: FetchProductsParams): Promise<ProductsResponse> {
  try {
    // In a real app, this would call the API
    // const response = await axios.get('/products', { params });
    // return response.data;
    
    // For demo, we'll return mock data with pagination
    return new Promise((resolve) => {
      setTimeout(() => {
        const { page, pageSize } = params;
        const start = (page - 1) * pageSize;
        const end = start + pageSize;
        
        const paginatedData = mockProducts.slice(start, end);
        
        resolve({
          data: paginatedData,
          total: mockProducts.length
        });
      }, 500);
    });
  } catch (error) {
    console.error('Error fetching products:', error);
    throw error;
  }
}

export async function fetchProduct(id: string): Promise<Product> {
  try {
    // In a real app, this would call the API
    // const response = await axios.get(`/products/${id}`);
    // return response.data;
    
    // For demo, we'll return mock data
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const product = mockProducts.find(p => p.id === id);
        if (product) {
          resolve(product);
        } else {
          reject(new Error('Product not found'));
        }
      }, 500);
    });
  } catch (error) {
    console.error(`Error fetching product ${id}:`, error);
    throw error;
  }
}

export async function createProduct(data: Partial<Product>): Promise<Product> {
  try {
    // In a real app, this would call the API
    // const response = await axios.post('/products', data);
    // return response.data;
    
    // For demo, we'll return mock success
    return new Promise((resolve) => {
      setTimeout(() => {
        const newProduct: Product = {
          id: String(mockProducts.length + 1),
          name: data.name || '',
          description: data.description || '',
          category: data.category || '',
          price: data.price || 0,
          stock: data.stock || 0,
          status: data.status || 'active',
          image: data.image,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        
        resolve(newProduct);
      }, 500);
    });
  } catch (error) {
    console.error('Error creating product:', error);
    throw error;
  }
}

export async function updateProduct(id: string, data: Partial<Product>): Promise<Product> {
  try {
    // In a real app, this would call the API
    // const response = await axios.put(`/products/${id}`, data);
    // return response.data;
    
    // For demo, we'll return mock success
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const productIndex = mockProducts.findIndex(p => p.id === id);
        if (productIndex !== -1) {
          const updatedProduct = {
            ...mockProducts[productIndex],
            ...data,
            updatedAt: new Date().toISOString()
          };
          
          resolve(updatedProduct);
        } else {
          reject(new Error('Product not found'));
        }
      }, 500);
    });
  } catch (error) {
    console.error(`Error updating product ${id}:`, error);
    throw error;
  }
}

export async function deleteProduct(id: string): Promise<boolean> {
  try {
    // In a real app, this would call the API
    // await axios.delete(`/products/${id}`);
    
    // For demo, we'll return mock success
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(true);
      }, 500);
    });
  } catch (error) {
    console.error(`Error deleting product ${id}:`, error);
    throw error;
  }
}