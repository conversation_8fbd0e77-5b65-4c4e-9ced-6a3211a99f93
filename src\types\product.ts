export interface Product {
  id: string;
  name: string;
  description: string;
  category: string;
  price: number;
  stock: number;
  status: 'active' | 'inactive';
  image?: string;
  createdAt: string;
  updatedAt?: string;
}

export interface FetchProductsParams {
  page: number;
  pageSize: number;
  search?: string;
  category?: string;
  status?: string;
  sortField?: string;
  sortOrder?: string;
}

export interface ProductsResponse {
  data: Product[];
  total: number;
}