<script setup lang="ts">
import { watch } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from './stores/auth';

const router = useRouter();
const authStore = useAuthStore();

// Watch authentication state and redirect if needed
watch(
  () => authStore.isAuthenticated,
  (isAuthenticated) => {
    const currentRoute = router.currentRoute.value;
    if (!isAuthenticated && !currentRoute.meta.public) {
      router.push({ name: 'login' });
    }
  },
  { immediate: true }
);
</script>

<template>
  <router-view />
</template>

<style>
#app {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  text-align: left;
}
</style>