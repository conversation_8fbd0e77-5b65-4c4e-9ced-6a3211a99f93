import axios from '../utils/axios';
import type { User } from '../types/auth';

interface FetchUsersParams {
  page: number;
  pageSize: number;
  search?: string;
  status?: string;
  sortField?: string;
  sortOrder?: string;
}

interface UsersResponse {
  data: User[];
  total: number;
}

// Mock data
const mockUsers: User[] = [
  {
    id: '1',
    username: 'admin',
    email: '<EMAIL>',
    name: 'Admin User',
    roles: ['admin'],
    status: 'active',
    createdAt: '2023-01-01T00:00:00Z',
    lastLogin: '2023-10-01T12:30:45Z'
  },
  {
    id: '2',
    username: 'john.doe',
    email: '<EMAIL>',
    name: '<PERSON>',
    roles: ['editor'],
    status: 'active',
    createdAt: '2023-02-15T00:00:00Z',
    lastLogin: '2023-09-28T08:15:22Z'
  },
  {
    id: '3',
    username: 'jane.smith',
    email: '<EMAIL>',
    name: '<PERSON>',
    roles: ['user'],
    status: 'inactive',
    createdAt: '2023-03-10T00:00:00Z',
    lastLogin: '2023-08-05T16:42:10Z'
  },
  // Add more mock users as needed
];

export async function fetchUsers(params: FetchUsersParams): Promise<UsersResponse> {
  try {
    // In a real app, this would call the API
    // const response = await axios.get('/users', { params });
    // return response.data;
    
    // For demo, we'll return mock data with pagination
    return new Promise((resolve) => {
      setTimeout(() => {
        const { page, pageSize } = params;
        const start = (page - 1) * pageSize;
        const end = start + pageSize;
        
        const paginatedData = mockUsers.slice(start, end);
        
        resolve({
          data: paginatedData,
          total: mockUsers.length
        });
      }, 500);
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    throw error;
  }
}

export async function fetchUser(id: string): Promise<User> {
  try {
    // In a real app, this would call the API
    // const response = await axios.get(`/users/${id}`);
    // return response.data;
    
    // For demo, we'll return mock data
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const user = mockUsers.find(u => u.id === id);
        if (user) {
          resolve(user);
        } else {
          reject(new Error('User not found'));
        }
      }, 500);
    });
  } catch (error) {
    console.error(`Error fetching user ${id}:`, error);
    throw error;
  }
}

export async function createUser(data: Partial<User>): Promise<User> {
  try {
    // In a real app, this would call the API
    // const response = await axios.post('/users', data);
    // return response.data;
    
    // For demo, we'll return mock success
    return new Promise((resolve) => {
      setTimeout(() => {
        const newUser: User = {
          id: String(mockUsers.length + 1),
          username: data.username || '',
          email: data.email || '',
          name: data.name || '',
          roles: data.roles || ['user'],
          status: data.status || 'active',
          createdAt: new Date().toISOString(),
          lastLogin: undefined
        };
        
        resolve(newUser);
      }, 500);
    });
  } catch (error) {
    console.error('Error creating user:', error);
    throw error;
  }
}

export async function updateUser(id: string, data: Partial<User>): Promise<User> {
  try {
    // In a real app, this would call the API
    // const response = await axios.put(`/users/${id}`, data);
    // return response.data;
    
    // For demo, we'll return mock success
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const userIndex = mockUsers.findIndex(u => u.id === id);
        if (userIndex !== -1) {
          const updatedUser = {
            ...mockUsers[userIndex],
            ...data
          };
          
          resolve(updatedUser);
        } else {
          reject(new Error('User not found'));
        }
      }, 500);
    });
  } catch (error) {
    console.error(`Error updating user ${id}:`, error);
    throw error;
  }
}

export async function deleteUser(id: string): Promise<boolean> {
  try {
    // In a real app, this would call the API
    // await axios.delete(`/users/${id}`);
    
    // For demo, we'll return mock success
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(true);
      }, 500);
    });
  } catch (error) {
    console.error(`Error deleting user ${id}:`, error);
    throw error;
  }
}