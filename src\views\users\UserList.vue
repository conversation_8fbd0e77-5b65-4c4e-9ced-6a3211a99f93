<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { fetchUsers, deleteUser } from '../../api/users';
import type { User } from '../../types/auth';

const router = useRouter();
const loading = ref(false);
const users = ref<User[]>([]);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50'],
});

const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 80,
  },
  {
    title: 'Username',
    dataIndex: 'username',
    key: 'username',
    sorter: true,
  },
  {
    title: 'Name',
    dataIndex: 'name',
    key: 'name',
    sorter: true,
  },
  {
    title: 'Email',
    dataIndex: 'email',
    key: 'email',
  },
  {
    title: 'Status',
    dataIndex: 'status',
    key: 'status',
    filters: [
      { text: 'Active', value: 'active' },
      { text: 'Inactive', value: 'inactive' },
    ],
  },
  {
    title: 'Created At',
    dataIndex: 'createdAt',
    key: 'createdAt',
    sorter: true,
  },
  {
    title: 'Actions',
    key: 'actions',
    width: 150,
  },
];

const fetchData = async () => {
  loading.value = true;
  try {
    const { data, total } = await fetchUsers({
      page: pagination.current,
      pageSize: pagination.pageSize,
    });
    users.value = data;
    pagination.total = total;
  } catch (error) {
    console.error('Failed to fetch users:', error);
    message.error('Failed to fetch users');
  } finally {
    loading.value = false;
  }
};

const handleTableChange = (pag: any, filters: any, sorter: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchData();
};

const handleEdit = (id: string) => {
  router.push(`/users/${id}/edit`);
};

const handleDelete = (id: string) => {
  Modal.confirm({
    title: 'Are you sure you want to delete this user?',
    icon: () => h(ExclamationCircleOutlined),
    content: 'This action cannot be undone.',
    okText: 'Yes',
    okType: 'danger',
    cancelText: 'No',
    async onOk() {
      try {
        await deleteUser(id);
        message.success('User deleted successfully');
        fetchData();
      } catch (error) {
        console.error('Failed to delete user:', error);
        message.error('Failed to delete user');
      }
    },
  });
};

const handleAddUser = () => {
  router.push('/users/create');
};

onMounted(() => {
  fetchData();
});
</script>

<template>
  <div class="user-list-container">
    <div class="page-header">
      <h1 class="page-title">Users</h1>
      <a-button type="primary" @click="handleAddUser">
        <template #icon><PlusOutlined /></template>
        Add User
      </a-button>
    </div>
    
    <a-card>
      <a-table
        :columns="columns"
        :data-source="users"
        :pagination="pagination"
        :loading="loading"
        @change="handleTableChange"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="record.status === 'active' ? 'green' : 'red'">
              {{ record.status }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'actions'">
            <a-space>
              <a-button type="primary" size="small" @click="handleEdit(record.id)">
                Edit
              </a-button>
              <a-button type="danger" size="small" @click="handleDelete(record.id)">
                Delete
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<style scoped>
.user-list-container {
  width: 100%;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}
</style>