<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import { fetchProduct, createProduct, updateProduct } from '../../api/products';
import type { Product } from '../../types/product';

const router = useRouter();
const route = useRoute();
const productId = route.params.id as string;
const isEdit = !!productId;
const loading = ref(false);
const submitting = ref(false);

const formState = reactive<Partial<Product>>({
  name: '',
  description: '',
  category: '',
  price: 0,
  stock: 0,
  status: 'active',
  image: ''
});

const rules = {
  name: [
    { required: true, message: 'Please input product name!', trigger: 'blur' },
    { min: 3, message: 'Name must be at least 3 characters', trigger: 'blur' }
  ],
  description: [
    { required: true, message: 'Please input product description!', trigger: 'blur' }
  ],
  category: [
    { required: true, message: 'Please select product category!', trigger: 'change' }
  ],
  price: [
    { required: true, message: 'Please input product price!', trigger: 'blur' },
    { type: 'number', min: 0.01, message: 'Price must be greater than 0', trigger: 'blur' }
  ],
  stock: [
    { required: true, message: 'Please input stock quantity!', trigger: 'blur' },
    { type: 'number', min: 0, message: 'Stock cannot be negative', trigger: 'blur' }
  ],
  status: [
    { required: true, message: 'Please select status!', trigger: 'change' }
  ]
};

const categoryOptions = [
  { label: 'Electronics', value: 'electronics' },
  { label: 'Clothing', value: 'clothing' },
  { label: 'Books', value: 'books' },
  { label: 'Food', value: 'food' },
  { label: 'Others', value: 'others' }
];

const statusOptions = [
  { label: 'Active', value: 'active' },
  { label: 'Inactive', value: 'inactive' }
];

const fetchProductData = async () => {
  if (!isEdit) return;
  
  loading.value = true;
  try {
    const product = await fetchProduct(productId);
    Object.assign(formState, product);
  } catch (error) {
    console.error('Failed to fetch product:', error);
    message.error('Failed to fetch product data');
    router.push('/products');
  } finally {
    loading.value = false;
  }
};

const handleSubmit = async () => {
  submitting.value = true;
  try {
    if (isEdit) {
      await updateProduct(productId, formState);
      message.success('Product updated successfully');
    } else {
      await createProduct(formState);
      message.success('Product created successfully');
    }
    router.push('/products');
  } catch (error) {
    console.error('Failed to save product:', error);
    message.error('Failed to save product');
  } finally {
    submitting.value = false;
  }
};

const handleCancel = () => {
  router.push('/products');
};

onMounted(() => {
  fetchProductData();
});
</script>

<template>
  <div class="product-form-container">
    <div class="page-header">
      <h1 class="page-title">{{ isEdit ? 'Edit Product' : 'Create Product' }}</h1>
    </div>
    
    <a-card :loading="loading">
      <a-form
        :model="formState"
        :rules="rules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 16 }"
        layout="horizontal"
      >
        <a-form-item label="Name" name="name">
          <a-input v-model:value="formState.name" />
        </a-form-item>
        
        <a-form-item label="Description" name="description">
          <a-textarea v-model:value="formState.description" :rows="4" />
        </a-form-item>
        
        <a-form-item label="Category" name="category">
          <a-select
            v-model:value="formState.category"
            :options="categoryOptions"
            placeholder="Select category"
          />
        </a-form-item>
        
        <a-form-item label="Price" name="price">
          <a-input-number
            v-model:value="formState.price"
            :min="0"
            :step="0.01"
            :formatter="value => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
            style="width: 100%"
          />
        </a-form-item>
        
        <a-form-item label="Stock" name="stock">
          <a-input-number
            v-model:value="formState.stock"
            :min="0"
            style="width: 100%"
          />
        </a-form-item>
        
        <a-form-item label="Status" name="status">
          <a-select
            v-model:value="formState.status"
            :options="statusOptions"
            placeholder="Select status"
          />
        </a-form-item>
        
        <a-form-item label="Image URL" name="image">
          <a-input v-model:value="formState.image" placeholder="https://example.com/image.jpg" />
        </a-form-item>
        
        <a-form-item :wrapper-col="{ offset: 4, span: 16 }">
          <a-space>
            <a-button type="primary" @click="handleSubmit" :loading="submitting">
              {{ isEdit ? 'Update' : 'Create' }}
            </a-button>
            <a-button @click="handleCancel">
              Cancel
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<style scoped>
.product-form-container {
  width: 100%;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}
</style>