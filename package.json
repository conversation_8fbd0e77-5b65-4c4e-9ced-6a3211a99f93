{"name": "vue3-admin-dashboard", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"ant-design-vue": "^4.0.0", "axios": "^1.6.2", "chart.js": "^4.4.0", "dayjs": "^1.11.10", "jwt-decode": "^4.0.0", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.0", "vue": "^3.4.38", "vue-chartjs": "^5.2.0", "vue-router": "^4.2.5"}, "devDependencies": {"@types/node": "^20.10.0", "@vitejs/plugin-vue": "^5.1.3", "less": "^4.2.0", "typescript": "^5.5.3", "vite": "^5.4.2", "vue-tsc": "^2.1.4"}}