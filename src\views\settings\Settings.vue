<script setup lang="ts">
import { ref, reactive } from 'vue';
import { message } from 'ant-design-vue';

const activeKey = ref('account');

const accountForm = reactive({
  name: 'Admin User',
  email: '<EMAIL>',
  avatar: '',
  bio: '',
});

const securityForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: '',
});

const notificationSettings = reactive({
  email: true,
  browser: true,
  system: false,
});

const securityRules = {
  currentPassword: [
    { required: true, message: 'Please input current password!', trigger: 'blur' },
  ],
  newPassword: [
    { required: true, message: 'Please input new password!', trigger: 'blur' },
    { min: 8, message: 'Password must be at least 8 characters', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: 'Please confirm new password!', trigger: 'blur' },
    {
      validator: (_: any, value: string) => {
        if (!value || securityForm.newPassword === value) {
          return Promise.resolve();
        }
        return Promise.reject(new Error('The two passwords do not match!'));
      },
      trigger: 'blur'
    }
  ]
};

const updateAccountInfo = () => {
  message.success('Account information updated successfully');
};

const changePassword = () => {
  message.success('Password changed successfully');
  securityForm.currentPassword = '';
  securityForm.newPassword = '';
  securityForm.confirmPassword = '';
};

const updateNotificationSettings = () => {
  message.success('Notification settings updated successfully');
};
</script>

<template>
  <div class="settings-container">
    <h1 class="page-title">Settings</h1>
    
    <a-card>
      <a-tabs v-model:activeKey="activeKey">
        <a-tab-pane key="account" tab="Account">
          <a-form
            :model="accountForm"
            layout="vertical"
          >
            <a-form-item label="Name" name="name">
              <a-input v-model:value="accountForm.name" />
            </a-form-item>
            
            <a-form-item label="Email" name="email">
              <a-input v-model:value="accountForm.email" />
            </a-form-item>
            
            <a-form-item label="Avatar URL" name="avatar">
              <a-input v-model:value="accountForm.avatar" placeholder="https://example.com/avatar.jpg" />
            </a-form-item>
            
            <a-form-item label="Bio" name="bio">
              <a-textarea v-model:value="accountForm.bio" :rows="4" placeholder="Tell us about yourself" />
            </a-form-item>
            
            <a-form-item>
              <a-button type="primary" @click="updateAccountInfo">
                Save Changes
              </a-button>
            </a-form-item>
          </a-form>
        </a-tab-pane>
        
        <a-tab-pane key="security" tab="Security">
          <a-form
            :model="securityForm"
            :rules="securityRules"
            layout="vertical"
          >
            <a-form-item label="Current Password" name="currentPassword">
              <a-input-password v-model:value="securityForm.currentPassword" />
            </a-form-item>
            
            <a-form-item label="New Password" name="newPassword">
              <a-input-password v-model:value="securityForm.newPassword" />
            </a-form-item>
            
            <a-form-item label="Confirm New Password" name="confirmPassword">
              <a-input-password v-model:value="securityForm.confirmPassword" />
            </a-form-item>
            
            <a-form-item>
              <a-button type="primary" @click="changePassword">
                Change Password
              </a-button>
            </a-form-item>
          </a-form>
        </a-tab-pane>
        
        <a-tab-pane key="notifications" tab="Notifications">
          <a-list>
            <a-list-item>
              <a-list-item-meta title="Email Notifications">
                <template #description>Receive notifications via email</template>
              </a-list-item-meta>
              <template #extra>
                <a-switch v-model:checked="notificationSettings.email" />
              </template>
            </a-list-item>
            
            <a-list-item>
              <a-list-item-meta title="Browser Notifications">
                <template #description>Receive browser push notifications</template>
              </a-list-item-meta>
              <template #extra>
                <a-switch v-model:checked="notificationSettings.browser" />
              </template>
            </a-list-item>
            
            <a-list-item>
              <a-list-item-meta title="System Notifications">
                <template #description>Receive system notifications</template>
              </a-list-item-meta>
              <template #extra>
                <a-switch v-model:checked="notificationSettings.system" />
              </template>
            </a-list-item>
          </a-list>
          
          <div style="margin-top: 24px">
            <a-button type="primary" @click="updateNotificationSettings">
              Save Notification Settings
            </a-button>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<style scoped>
.settings-container {
  width: 100%;
}

.page-title {
  margin-bottom: 24px;
  font-size: 24px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}
</style>