import axios from '../utils/axios';
import { UserLoginData, LoginResponse } from '../types/auth';

export async function login(data: UserLoginData): Promise<LoginResponse> {
  try {
    // In a real app, this would be a real API endpoint
    // For demo purposes, we'll simulate a successful login
    if (data.username === 'admin' && data.password === 'admin123') {
      return {
        token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjEiLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZXMiOlsiYWRtaW4iXSwicGVybWlzc2lvbnMiOlsidXNlcnMucmVhZCIsInVzZXJzLndyaXRlIiwicHJvZHVjdHMucmVhZCIsInByb2R1Y3RzLndyaXRlIl19.8tIIYiCf6nzbSgGoVHzFk_9nSWP5uJnHQHVQXN4-WD0',
        user: {
          id: '1',
          username: 'admin',
          roles: ['admin']
        }
      };
    }
    
    throw new Error('Invalid credentials');
    
    // Uncomment for real API implementation
    // const response = await axios.post('/auth/login', data);
    // return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new Error(error.response.data.message || 'Login failed');
    }
    throw error;
  }
}

export async function refreshToken(token: string): Promise<LoginResponse> {
  try {
    // In a real app, this would be a real API endpoint
    // For demo purposes, we'll just return the same token
    return {
      token,
      user: {
        id: '1',
        username: 'admin',
        roles: ['admin']
      }
    };
    
    // Uncomment for real API implementation
    // const response = await axios.post('/auth/refresh', { token });
    // return response.data;
  } catch (error: any) {
    if (error.response) {
      throw new Error(error.response.data.message || 'Failed to refresh token');
    }
    throw error;
  }
}