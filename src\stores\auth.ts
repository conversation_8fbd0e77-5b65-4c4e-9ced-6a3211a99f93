import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { jwtDecode } from 'jwt-decode';
import { login as loginApi } from '../api/auth';
import { UserLoginData } from '../types/auth';

export const useAuthStore = defineStore('auth', () => {
  // State
  const token = ref<string | null>(null);
  const user = ref<any>(null);
  const loading = ref(false);
  const error = ref<string | null>(null);

  // Getters
  const isAuthenticated = computed(() => !!token.value);
  
  const userRoles = computed(() => {
    if (!user.value) return [];
    return user.value.roles || [];
  });

  // Actions
  async function login(userData: UserLoginData) {
    loading.value = true;
    error.value = null;
    
    try {
      const response = await loginApi(userData);
      token.value = response.token;
      
      // Decode token to get user data
      const decoded = jwtDecode(response.token);
      user.value = decoded;
      
      return true;
    } catch (err: any) {
      error.value = err.message || 'Login failed';
      return false;
    } finally {
      loading.value = false;
    }
  }

  function logout() {
    token.value = null;
    user.value = null;
  }

  function hasPermission(permission: string): boolean {
    if (!user.value || !user.value.permissions) return false;
    return user.value.permissions.includes(permission);
  }

  function hasRole(role: string): boolean {
    return userRoles.value.includes(role);
  }

  return {
    token,
    user,
    loading,
    error,
    isAuthenticated,
    userRoles,
    login,
    logout,
    hasPermission,
    hasRole
  };
}, {
  persist: {
    key: 'auth-store',
    storage: localStorage,
    paths: ['token', 'user']
  }
});