<script setup lang="ts">
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { UserOutlined, LockOutlined } from '@ant-design/icons-vue';
import { useAuthStore } from '../../stores/auth';
import type { UserLoginData } from '../../types/auth';

const authStore = useAuthStore();
const router = useRouter();
const loading = ref(false);

const formState = reactive<UserLoginData>({
  username: '',
  password: '',
  remember: true,
});

const rules = {
  username: [
    { required: true, message: 'Please input your username!' }
  ],
  password: [
    { required: true, message: 'Please input your password!' }
  ],
};

const handleSubmit = async () => {
  loading.value = true;
  try {
    const success = await authStore.login({
      username: formState.username,
      password: formState.password,
      remember: formState.remember
    });
    
    if (success) {
      message.success('Login successful!');
      router.push('/dashboard');
    } else {
      message.error(authStore.error || 'Login failed');
    }
  } catch (error: any) {
    message.error(error.message || 'Login failed');
  } finally {
    loading.value = false;
  }
};

// Demo credentials hint
const setDemoCredentials = () => {
  formState.username = 'admin';
  formState.password = 'admin123';
};
</script>

<template>
  <div class="login-form">
    <a-form
      :model="formState"
      :rules="rules"
      @finish="handleSubmit"
    >
      <a-form-item name="username">
        <a-input
          v-model:value="formState.username"
          placeholder="Username"
          size="large"
        >
          <template #prefix>
            <user-outlined />
          </template>
        </a-input>
      </a-form-item>
      
      <a-form-item name="password">
        <a-input-password
          v-model:value="formState.password"
          placeholder="Password"
          size="large"
        >
          <template #prefix>
            <lock-outlined />
          </template>
        </a-input-password>
      </a-form-item>
      
      <a-form-item>
        <a-checkbox v-model:checked="formState.remember">
          Remember me
        </a-checkbox>
        <a class="login-form-forgot" href="javascript:void(0)">
          Forgot password
        </a>
      </a-form-item>

      <a-form-item>
        <a-button
          type="primary"
          html-type="submit"
          size="large"
          :loading="loading"
          class="login-form-button"
        >
          Log in
        </a-button>
      </a-form-item>
      
      <a-alert
        type="info"
        show-icon
        message="Demo Credentials"
        description="Username: admin, Password: admin123"
        style="margin-bottom: 24px"
        action-text="Use Demo"
        @close="setDemoCredentials"
      />
    </a-form>
  </div>
</template>

<style scoped>
.login-form {
  width: 100%;
}

.login-form-forgot {
  float: right;
}

.login-form-button {
  width: 100%;
}
</style>