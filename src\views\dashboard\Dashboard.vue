<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { Line, Bar, Pie, Doughnut } from 'vue-chartjs';
import { Chart as ChartJS, registerables } from 'chart.js';
import { fetchDashboardStats } from '../../api/dashboard';

ChartJS.register(...registerables);

const stats = ref({
  users: 0,
  products: 0,
  orders: 0,
  revenue: 0
});

const loading = ref(true);

const salesData = {
  labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],
  datasets: [
    {
      label: 'Sales',
      backgroundColor: 'rgba(24, 144, 255, 0.2)',
      borderColor: '#1890ff',
      data: [0, 59, 80, 81, 56, 55, 40]
    }
  ]
};

const userActivityData = {
  labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
  datasets: [
    {
      label: 'Active Users',
      backgroundColor: '#13c2c2',
      data: [43, 85, 72, 90, 80, 95, 70]
    }
  ]
};

const productCategoryData = {
  labels: ['Electronics', 'Clothing', 'Books', 'Food', 'Others'],
  datasets: [
    {
      backgroundColor: ['#1890ff', '#13c2c2', '#52c41a', '#faad14', '#f5222d'],
      data: [300, 150, 100, 80, 40]
    }
  ]
};

const revenueSourceData = {
  labels: ['Online Store', 'Mobile App', 'Marketplaces', 'Social Media'],
  datasets: [
    {
      backgroundColor: ['#722ed1', '#eb2f96', '#faad14', '#52c41a'],
      data: [55, 25, 15, 5]
    }
  ]
};

onMounted(async () => {
  try {
    const data = await fetchDashboardStats();
    stats.value = data;
  } catch (error) {
    console.error('Failed to fetch dashboard stats:', error);
  } finally {
    loading.value = false;
  }
});
</script>

<template>
  <div class="dashboard-container">
    <h1 class="page-title">Dashboard</h1>
    
    <a-row :gutter="[16, 16]">
      <!-- Stats Cards -->
      <a-col :xs="24" :sm="12" :lg="6">
        <a-card>
          <a-statistic
            title="Total Users"
            :value="stats.users"
            :loading="loading"
          >
            <template #prefix>
              <UserOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      
      <a-col :xs="24" :sm="12" :lg="6">
        <a-card>
          <a-statistic
            title="Products"
            :value="stats.products"
            :loading="loading"
          >
            <template #prefix>
              <ShoppingOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      
      <a-col :xs="24" :sm="12" :lg="6">
        <a-card>
          <a-statistic
            title="Orders"
            :value="stats.orders"
            :loading="loading"
          >
            <template #prefix>
              <ShoppingCartOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      
      <a-col :xs="24" :sm="12" :lg="6">
        <a-card>
          <a-statistic
            title="Revenue"
            prefix="$"
            :value="stats.revenue"
            :precision="2"
            :loading="loading"
          >
            <template #prefix>
              <DollarOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      
      <!-- Charts -->
      <a-col :xs="24" :lg="12">
        <a-card title="Sales Overview" :loading="loading">
          <Line 
            :data="salesData" 
            :options="{
              responsive: true,
              maintainAspectRatio: false
            }"
          />
        </a-card>
      </a-col>
      
      <a-col :xs="24" :lg="12">
        <a-card title="User Activity" :loading="loading">
          <Bar 
            :data="userActivityData" 
            :options="{
              responsive: true,
              maintainAspectRatio: false
            }"
          />
        </a-card>
      </a-col>
      
      <a-col :xs="24" :sm="12">
        <a-card title="Product Categories" :loading="loading">
          <Pie 
            :data="productCategoryData"
            :options="{
              responsive: true,
              maintainAspectRatio: false
            }"
          />
        </a-card>
      </a-col>
      
      <a-col :xs="24" :sm="12">
        <a-card title="Revenue Sources" :loading="loading">
          <Doughnut 
            :data="revenueSourceData"
            :options="{
              responsive: true,
              maintainAspectRatio: false
            }"
          />
        </a-card>
      </a-col>
      
      <!-- Recent Activity -->
      <a-col :span="24">
        <a-card title="Recent Activity" :loading="loading">
          <a-timeline>
            <a-timeline-item>New user registered - John Doe</a-timeline-item>
            <a-timeline-item>Order #12345 processed</a-timeline-item>
            <a-timeline-item>System update completed</a-timeline-item>
            <a-timeline-item>New product added - Wireless Headphones</a-timeline-item>
          </a-timeline>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<style scoped>
.dashboard-container {
  width: 100%;
}

.page-title {
  margin-bottom: 24px;
  font-size: 24px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}

.ant-card {
  height: 100%;
}

:deep(.ant-statistic-title) {
  font-size: 14px;
}

:deep(.ant-timeline) {
  padding: 12px;
}
</style>