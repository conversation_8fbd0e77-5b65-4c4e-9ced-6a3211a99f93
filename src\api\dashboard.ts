// Mock dashboard API

interface DashboardStats {
  users: number;
  products: number;
  orders: number;
  revenue: number;
}

export async function fetchDashboardStats(): Promise<DashboardStats> {
  // Simulating API call
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        users: 1258,
        products: 486,
        orders: 352,
        revenue: 56789.45
      });
    }, 1000);
  });
}