import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router';
import { useAuthStore } from '../stores/auth';

// Layouts
import AuthLayout from '../layouts/AuthLayout.vue';
import DashboardLayout from '../layouts/DashboardLayout.vue';

// Auth views
import Login from '../views/auth/Login.vue';

// Dashboard views
import Dashboard from '../views/dashboard/Dashboard.vue';
import UserList from '../views/users/UserList.vue';
import UserForm from '../views/users/UserForm.vue';
import ProductList from '../views/products/ProductList.vue';
import ProductForm from '../views/products/ProductForm.vue';
import Settings from '../views/settings/Settings.vue';

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/auth',
    component: AuthLayout,
    children: [
      {
        path: 'login',
        name: 'login',
        component: Login,
        meta: { public: true }
      }
    ]
  },
  {
    path: '/',
    component: DashboardLayout,
    meta: { requiresAuth: true },
    children: [
      {
        path: 'dashboard',
        name: 'dashboard',
        component: Dashboard
      },
      {
        path: 'users',
        name: 'users',
        component: UserList
      },
      {
        path: 'users/create',
        name: 'user-create',
        component: UserForm
      },
      {
        path: 'users/:id/edit',
        name: 'user-edit',
        component: UserForm,
        props: true
      },
      {
        path: 'products',
        name: 'products',
        component: ProductList
      },
      {
        path: 'products/create',
        name: 'product-create',
        component: ProductForm
      },
      {
        path: 'products/:id/edit',
        name: 'product-edit',
        component: ProductForm,
        props: true
      },
      {
        path: 'settings',
        name: 'settings',
        component: Settings
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/dashboard'
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

// Navigation guard
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore();
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth);
  const isPublic = to.matched.some(record => record.meta.public);

  if (requiresAuth && !authStore.isAuthenticated) {
    next({ name: 'login' });
  } else if (authStore.isAuthenticated && isPublic) {
    next({ name: 'dashboard' });
  } else {
    next();
  }
});

export default router;